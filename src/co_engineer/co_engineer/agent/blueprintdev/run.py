import importlib
import yaml
import argparse
import os
import threading
import asyncio
import traceback
from mcp import StdioServerParameters
from dotenv import load_dotenv

# from dotenv import load_dotenv

from smolagents import (
    ToolCallingAgent,
    OpenAIServerModel,
    MCPClient,
    ToolCollection
)


# Load environment variables from .env file
load_dotenv()

# load_dotenv(override=True)
append_answer_lock = threading.Lock()

# def parse_args():
#     parser = argparse.ArgumentParser()
#     parser.add_argument(
#         "task", type=str, help="for example: 'create nano to print out helloworld?'"
#     )
#     return parser.parse_args()

# Initialize variables to hold the initialized components
model = None
mcp_fs_params = None
mcp_cli_params = None
mcp_github_params = None
knowledge_based = None
prompt_templates = None

# Async initialization functions with exception handling
async def init_model():
    global model
    try:
        model = OpenAIServerModel(
            model_id="Qwen/Qwen3-235B-A22B",
            api_base="http://100.80.20.5:4000/v1",
            api_key="API_KEY"
        )
        print("Model initialized successfully")
    except Exception as e:
        print(f"Error initializing model: {e}")
        traceback.print_exc()


async def init_mcp_fs_params():
    global mcp_fs_params
    try:
        mcp_fs_params = StdioServerParameters(
            command="mcp-filesystem-server",
            args=["/home/<USER>/sandbox/POC6"],
            env={
                **os.environ
            },
        )
        print("MCP filesystem parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing MCP filesystem parameters: {e}")
        traceback.print_exc()


async def init_mcp_cli_params():
    global mcp_cli_params
    try:
        mcp_cli_params = StdioServerParameters(
            command="uvx",
            args=["cli-mcp-server"],
            env={
                "ALLOWED_DIR": "/home/<USER>/sandbox/POC6",
                "ALLOWED_COMMANDS": "all",
                "ALLOWED_FLAGS": "all",
                "MAX_COMMAND_LENGTH": "1024",
                "COMMAND_TIMEOUT": "300",
                "ALLOW_SHELL_OPERATORS": "true",
                **os.environ,
            },
        )
        print("MCP CLI parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing MCP CLI parameters: {e}")
        traceback.print_exc()

# async def init_mcp_github_params():
#     global mcp_github_params
#     try:
#         mcp_github_params = StdioServerParameters(
#             command="docker",
#             args=["run", "-i", "--rm",
#                     "-e", "GITHUB_PERSONAL_ACCESS_TOKEN",
#                     "deep-nexus.fln.delllabs.net:8085/x-mcp/mcp-github"
#                 ],
#             env={
#                 **os.environ
#             }
#         )
#         print("MCP GitHub parameters initialized successfully")
#     except Exception as e:
#         print(f"Error initializing MCP GitHub parameters: {e}")
#         traceback.print_exc()

async def init_knowledge_based():
    global knowledge_based
    try:
        knowledge_based = StdioServerParameters(
            command="uv",
            args=[ "run",
                "co_engineer/agent/knowledge_based/main.py"
                ],
            env={
                "R2R_API_URL": "http://192.168.201.152:7272",
                "ALL_PROXY": "socks5://100.80.20.1:3389"
            }
        )
        print("Knowledge base parameters initialized successfully")
    except Exception as e:
        print(f"Error initializing knowledge base parameters: {e}")
        traceback.print_exc()

async def init_prompt_templates():
    global prompt_templates
    try:
        prompt_templates = yaml.safe_load(
            importlib.resources.files("co_engineer.agent.blueprintdev.prompts").joinpath("blueprintdev_agent.yaml").read_text()
        )
        print("Prompt templates loaded successfully")
    except Exception as e:
        print(f"Error loading prompt templates: {e}")
        traceback.print_exc()

# Main initialization function that runs all initializations concurrently
async def initialize_all():
    tasks = [
        init_model(),
        init_mcp_fs_params(),
        init_mcp_cli_params(),
        # init_mcp_github_params(),
        init_knowledge_based(),
        init_prompt_templates()
    ]
    await asyncio.gather(*tasks)
    print("All initializations completed")

# Run the initialization
def initialize():
    asyncio.run(initialize_all())


def run_task(task):
    # Make sure all components are initialized
    if None in [model, mcp_fs_params, mcp_cli_params, knowledge_based, prompt_templates]:
        print("Some components are not initialized. Running initialization...")
        initialize()

    try:
        with ToolCollection.from_mcp([mcp_fs_params, mcp_cli_params, knowledge_based], trust_remote_code=True) as tool_collection:

            blueprintdev_agent = ToolCallingAgent(
                model=model,
                prompt_templates=prompt_templates,
                tools=[*tool_collection.tools],
                max_steps=120,
                verbosity_level=3,
                planning_interval=2,
                add_base_tools=False,
            )
            blueprintdev_agent.run(task=task)
    except Exception as e:
        print(f"Error running task: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    # Initialize all components asynchronously
    print("Starting initialization...")
    initialize()

    # Run the task after initialization is complete
    task = "create an empty file named /home/<USER>/helloworld.txt"
    run_task(task)
