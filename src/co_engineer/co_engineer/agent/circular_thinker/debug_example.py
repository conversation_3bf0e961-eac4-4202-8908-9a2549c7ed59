#!/usr/bin/env python3
"""
Debug功能使用示例

这个脚本展示了如何使用debug标志来查看JSON格式的OpenAI请求和响应内容。
"""

from utils.llm_client import get_client, TextPrompt, ToolParam

def main():
    """演示debug功能的主函数"""

    # 创建一个带有debug功能的客户端
    print("=== 创建带有debug功能的OpenAI客户端 ===")
    client = get_client(
        "openai-direct",
        model_name="deepseek-ai/DeepSeek-R1-0528",
        debug=True  # 启用debug模式
    )

    # 准备一些示例消息
    messages = [
        [TextPrompt(text="你好，请帮我写一个Python函数来计算斐波那契数列。")]
    ]

    # 准备一些示例工具
    tools = [
        ToolParam(
            name="write_code",
            description="写代码的工具",
            input_schema={
                "type": "object",
                "properties": {
                    "language": {
                        "type": "string",
                        "description": "编程语言"
                    },
                    "code": {
                        "type": "string",
                        "description": "代码内容"
                    }
                },
                "required": ["language", "code"]
            }
        )
    ]

    print("\n=== 调用LLM客户端（将显示JSON格式的debug信息）===")
    print("注意：debug模式将显示完整的OpenAI请求和响应的JSON格式")
    try:
        # 调用generate方法，这将触发debug输出
        # 你将看到两个JSON输出：
        # 1. OpenAI Request - 发送给API的完整请求
        # 2. OpenAI Response - 从API接收的完整响应
        response, metadata = client.generate(
            messages=messages,
            max_tokens=1000,
            system_prompt="你是一个有用的编程助手。",
            temperature=0.7,
            tools=tools
        )

        print("\n=== 处理后的响应结果 ===")
        print(f"响应类型: {type(response)}")
        print(f"响应内容: {response}")
        print(f"元数据: {metadata}")

    except Exception as e:
        print(f"调用失败: {e}")
        print("这可能是因为没有配置正确的API密钥或网络连接问题。")
        print("即使调用失败，你也应该能看到请求的JSON格式输出。")

if __name__ == "__main__":
    main()
