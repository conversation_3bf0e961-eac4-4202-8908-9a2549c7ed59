# Deep Research 快速入门指南

## 🎯 5分钟学会使用 Deep Research

Deep Research 是一个智能研究助手，就像有一个专业的研究员帮您快速了解任何主题。

### 🚀 三步开始研究

#### 第1步：输入您想了解的内容
在左侧的文本框中输入您的问题，比如：
- "什么是人工智能？"
- "如何学习编程？"
- "Docker是什么？"

#### 第2步：选择研究深度（可选）
- **Quick（快速）**：2-3分钟，基本了解
- **Standard（标准）**：3-5分钟，详细介绍（推荐新手）
- **Deep（深入）**：5-8分钟，全面分析

#### 第3步：点击"Start Research"
等待几分钟，右侧会显示完整的研究报告！

## 📱 界面简介

```
┌─────────────────┬─────────────────────────┐
│   左侧控制面板    │      右侧报告区域        │
│                │                        │
│ 📝 输入研究主题   │ 📄 显示研究报告          │
│ ⚙️  设置参数     │ 📋 复制/下载功能         │
│ 📊 查看进度     │ ✏️  编辑报告            │
│                │                        │
└─────────────────┴─────────────────────────┘
```

## 💡 新手推荐设置

**第一次使用建议**：
- 深度：选择 "Standard"
- 广度：选择 "Balanced" 
- 网络搜索：保持开启（获得最新信息）

## 🎯 适合新手的研究主题示例

### 技术入门类
- "什么是云计算？它有什么优势？"
- "编程语言Python适合做什么？"
- "网站是如何工作的？"

### 学习指导类  
- "如何开始学习数据分析？"
- "零基础学习网页设计的步骤"
- "新手如何选择编程语言？"

### 概念解释类
- "区块链技术是什么？如何应用？"
- "人工智能和机器学习的区别"
- "什么是开源软件？"

## 📊 看懂研究进度

研究过程中，左侧会显示进度：

1. **🔍 Searching**：正在搜索信息
2. **✅ Search Result**：找到相关内容
3. **🤖 Analyzing**：AI正在分析
4. **📝 Generating**：生成报告中

**绿色标签** = 找到相关信息  
**灰色标签** = 信息不太相关

## 📋 使用研究结果

### 查看报告
- 报告会自动显示在右侧
- 包含详细解释和要点总结
- 支持代码示例和图表

### 保存报告
- **Copy**：复制内容到其他地方
- **Download**：下载为文件保存

### 改进报告
- 点击 **Edit** 可以修改内容
- 在反馈框输入"请添加更多示例"等建议
- 点击 **Apply Feedback** 让AI改进

## ❓ 常见新手问题

### Q: 我不知道该搜索什么？
A: 从您感兴趣的话题开始，比如：
- "我想学习编程，应该从哪里开始？"
- "什么是最流行的编程语言？"

### Q: 搜索需要多长时间？
A: 通常2-5分钟，您可以看到实时进度。

### Q: 结果太复杂看不懂怎么办？
A: 在反馈框输入"请用更简单的语言解释"，然后点击Apply Feedback。

### Q: 可以搜索中文内容吗？
A: 可以！支持中英文搜索和结果显示。

### Q: 网络搜索显示不可用？
A: 没关系，系统仍会搜索内部知识库为您提供专业内容。

## 🎓 进阶技巧

### 写出更好的搜索主题
**✅ 好的例子：**
- "解释什么是API，并给出实际应用例子"
- "比较iPhone和Android手机的优缺点"

**❌ 避免的例子：**
- "API"（太简单）
- "告诉我关于手机的一切"（太宽泛）

### 获得更好的结果
1. **具体描述**：说明您想了解的具体方面
2. **包含背景**：提到您的知识水平或使用场景
3. **明确目标**：说明您想用这些信息做什么

### 示例对比

**普通搜索**：
"Python"

**更好的搜索**：
"Python编程语言适合初学者吗？学习Python可以做什么项目？"

## 🎉 开始您的第一次研究

现在就试试这些简单主题：

1. "什么是ChatGPT？它是如何工作的？"
2. "云存储和本地存储有什么区别？"
3. "如何保护个人网络安全？"

**记住**：
- 选择 "Standard" 深度
- 保持默认设置
- 耐心等待结果
- 尝试使用反馈功能改进结果

---

**祝您研究愉快！** 🚀

> 💡 **提示**：如需更详细的功能说明，请查看完整的 [USER_GUIDE.md](./USER_GUIDE.md)
