# Deep Research Quick Start Guide

## 🎯 Master Deep Research in 5 Minutes

Deep Research is an intelligent research assistant that acts like having a professional researcher help you quickly understand any topic.

### 🚀 Three Steps to Start Research

#### Step 1: Enter What You Want to Learn
In the text box on the left, type your question, such as:
- "What is artificial intelligence?"
- "How to learn programming?"
- "What is Docker?"

#### Step 2: Choose Research Depth (Optional)
- **Quick**: 2-3 minutes, basic understanding
- **Standard**: 3-5 minutes, detailed introduction (recommended for beginners)
- **Deep**: 5-8 minutes, comprehensive analysis

#### Step 3: Click "Start Research"
Wait a few minutes, and a complete research report will appear on the right!

## 📱 Interface Overview

```
┌─────────────────┬─────────────────────────┐
│   Left Panel    │      Right Panel        │
│                 │                         │
│ 📝 Research Topic│ 📄 Research Report      │
│ ⚙️  Parameters   │ 📋 Copy/Download        │
│ 📊 Progress     │ ✏️  Edit Report         │
│                 │                         │
└─────────────────┴─────────────────────────┘
```

## 💡 Recommended Settings for Beginners

**First-time use suggestions**:
- Depth: Choose "Standard"
- Breadth: Choose "Balanced" 
- Web Search: Keep enabled (get latest information)

## 🎯 Beginner-Friendly Research Topic Examples

### Technology Introduction
- "What is cloud computing? What are its advantages?"
- "What can Python programming language do?"
- "How do websites work?"

### Learning Guidance  
- "How to start learning data analysis?"
- "Steps to learn web design from scratch"
- "How should beginners choose a programming language?"

### Concept Explanation
- "What is blockchain technology? How is it applied?"
- "Difference between AI and machine learning"
- "What is open source software?"

## 📊 Understanding Research Progress

During research, the left side shows progress:

1. **🔍 Searching**: Looking for information
2. **✅ Search Result**: Found relevant content
3. **🤖 Analyzing**: AI is analyzing
4. **📝 Generating**: Creating report

**Green labels** = Found relevant information  
**Gray labels** = Information not very relevant

## 📋 Using Research Results

### View Report
- Report automatically displays on the right
- Includes detailed explanations and key points
- Supports code examples and charts

### Save Report
- **Copy**: Copy content elsewhere
- **Download**: Download as file

### Improve Report
- Click **Edit** to modify content
- Enter suggestions like "please add more examples" in feedback box
- Click **Apply Feedback** for AI improvements

## ❓ Common Beginner Questions

### Q: I don't know what to search for?
A: Start with topics you're interested in, like:
- "I want to learn programming, where should I start?"
- "What are the most popular programming languages?"

### Q: How long does searching take?
A: Usually 2-5 minutes, you can see real-time progress.

### Q: Results are too complex to understand?
A: Enter "please explain in simpler language" in feedback box, then click Apply Feedback.

### Q: Can I search in Chinese?
A: Yes! Supports both Chinese and English search and results.

### Q: Web search shows unavailable?
A: No problem, the system will still search internal knowledge base for professional content.

## 🎓 Advanced Tips

### Writing Better Search Topics
**✅ Good examples:**
- "Explain what APIs are and give practical application examples"
- "Compare advantages and disadvantages of iPhone vs Android phones"

**❌ Avoid:**
- "API" (too simple)
- "Tell me everything about phones" (too broad)

### Getting Better Results
1. **Specific description**: Explain what specific aspects you want to know
2. **Include background**: Mention your knowledge level or use case
3. **Clear goals**: Say what you want to do with this information

### Example Comparison

**Regular search**:
"Python"

**Better search**:
"Is Python programming language suitable for beginners? What projects can I do learning Python?"

## 🎉 Start Your First Research

Try these simple topics now:

1. "What is ChatGPT? How does it work?"
2. "What's the difference between cloud storage and local storage?"
3. "How to protect personal network security?"

**Remember**:
- Choose "Standard" depth
- Keep default settings
- Wait patiently for results
- Try using feedback feature to improve results

---

**Happy researching!** 🚀

> 💡 **Tip**: For more detailed feature explanations, see the complete [USER_GUIDE_EN.md](./USER_GUIDE_EN.md)
