# Deep Research UI 用户指南

## 🎯 什么是 Deep Research？

Deep Research 是一个智能研究助手，它可以帮助您快速学习和了解任何主题。无论您想了解技术概念、行业知识，还是学术研究，这个工具都能为您提供全面、准确的研究报告。

### 🌟 主要特点
- **智能搜索**：同时搜索互联网和内部知识库
- **实时进度**：看到AI助手的研究过程
- **专业报告**：生成结构化的Markdown格式报告
- **可编辑结果**：可以修改和完善研究结果
- **多种导出**：支持复制和下载报告

## 🚀 快速开始

### 第一步：访问系统
打开浏览器，访问：`https://your-server-address/`

> 💡 **提示**：如果看到安全警告，这是正常的（使用了自签名证书），点击"继续访问"即可。

### 第二步：了解界面布局

界面分为两个主要区域：

**左侧控制面板**：
- 研究主题输入框
- 研究参数设置
- 实时进度显示

**右侧报告区域**：
- 研究报告显示
- 编辑和导出功能

## 📝 如何进行研究

### 1. 输入研究主题

在"Research Topic"文本框中输入您想了解的主题，例如：
- "什么是Kubernetes？"
- "人工智能在医疗领域的应用"
- "区块链技术的工作原理"
- "如何配置Docker容器"

### 2. 设置研究参数

#### 深度 (Depth)
- **1 - Quick**：快速概览，适合了解基本概念
- **2 - Standard**：标准深度，平衡速度和详细程度（推荐）
- **3 - Deep**：深入研究，获得最全面的信息

#### 广度 (Breadth)
- **2 - Focused**：专注于核心内容
- **3 - Balanced**：平衡覆盖相关主题（推荐）
- **4 - Broad**：广泛覆盖相关领域

#### 网络搜索开关
- **开启**：同时搜索互联网和知识库（获得最新信息）
- **关闭**：仅搜索内部知识库（更快速度，专业内容）

### 3. 开始研究

点击"Start Research"按钮，系统将开始工作。您可以在左侧看到实时进度：

- 🔍 **搜索阶段**：AI正在搜索相关信息
- ✅ **结果评估**：评估搜索结果的相关性
- 📊 **报告生成**：整理信息并生成报告

### 4. 查看结果

研究完成后，右侧将显示完整的研究报告，包括：
- 执行摘要
- 详细分析
- 关键要点
- 参考资料

## 🛠 高级功能

### 编辑报告
1. 点击右上角的"Edit"按钮
2. 在编辑器中修改内容
3. 点击"Done"保存修改

### 反馈调整
研究完成后，您可以：
1. 在反馈框中输入调整建议
2. 点击"Apply Feedback"让AI改进报告
3. 上传相关文档来增强研究内容

### 导出报告
- **Copy**：复制报告内容到剪贴板
- **Download**：下载为Markdown文件

## 💡 使用技巧

### 如何写好研究主题？

**✅ 好的例子：**
- "解释Docker容器化技术的优势和应用场景"
- "比较React和Vue.js框架的特点"
- "分析云计算在企业数字化转型中的作用"

**❌ 避免的例子：**
- "Docker"（太简单）
- "告诉我所有关于AI的事情"（太宽泛）

### 选择合适的参数

**快速了解新概念**：
- 深度：1-2
- 广度：2-3
- 网络搜索：开启

**深入学习专业知识**：
- 深度：2-3
- 广度：3-4
- 网络搜索：根据需要

**查找内部文档**：
- 深度：1-2
- 广度：2-3
- 网络搜索：关闭

## 🔧 常见问题

### Q: 研究需要多长时间？
A: 通常2-5分钟，取决于主题复杂度和参数设置。

### Q: 可以同时进行多个研究吗？
A: 不可以，需要等待当前研究完成。

### Q: 如何获得更准确的结果？
A: 
- 使用具体、明确的主题描述
- 选择合适的深度和广度参数
- 开启网络搜索获取最新信息

### Q: 报告支持哪些格式？
A: 支持Markdown格式，包含代码高亮、图表和链接。

### Q: 网络搜索不可用怎么办？
A: 系统会自动检测并显示状态，仍可使用知识库搜索。

## 🎓 学习建议

### 对于初学者：
1. 从简单主题开始（深度1-2）
2. 逐步增加复杂度
3. 利用反馈功能完善结果

### 对于专业用户：
1. 使用具体的技术术语
2. 设置较高的深度和广度
3. 结合网络搜索和知识库

### 最佳实践：
- 保存重要的研究报告
- 定期更新研究内容
- 与团队分享有价值的发现

## 📞 技术支持

如果遇到问题：
1. 检查网络连接
2. 刷新页面重试
3. 查看浏览器控制台错误信息
4. 联系系统管理员

## 📚 实际使用场景

### 场景1：技术学习
**目标**：了解新技术概念
**示例主题**：
- "什么是微服务架构？它与单体架构有什么区别？"
- "Kubernetes的核心组件和工作原理"
- "GraphQL相比REST API的优势"

**推荐设置**：深度2，广度3，开启网络搜索

### 场景2：问题解决
**目标**：解决具体技术问题
**示例主题**：
- "如何在Ubuntu上安装和配置Nginx"
- "Docker容器内存使用过高的排查方法"
- "Python中如何处理大文件的内存优化"

**推荐设置**：深度2-3，广度2-3，开启网络搜索

### 场景3：行业调研
**目标**：了解行业趋势和最佳实践
**示例主题**：
- "2024年云计算发展趋势分析"
- "企业数字化转型的关键成功因素"
- "人工智能在金融行业的应用现状"

**推荐设置**：深度3，广度4，开启网络搜索

### 场景4：内部文档查询
**目标**：查找公司内部知识
**示例主题**：
- "Dell PowerStore存储配置最佳实践"
- "OpenShift集群部署流程"
- "内部API使用指南"

**推荐设置**：深度1-2，广度2-3，关闭网络搜索

## 🎯 报告质量优化

### 如何获得高质量报告？

1. **明确研究目标**
   - 具体说明您想了解什么
   - 包含关键词和上下文
   - 避免过于宽泛的主题

2. **合理设置参数**
   - 新手学习：深度1-2，广度2-3
   - 深入研究：深度2-3，广度3-4
   - 快速查询：深度1，广度2

3. **利用反馈功能**
   - 指出需要补充的内容
   - 要求更详细的解释
   - 请求具体的示例或代码

### 报告结构说明

生成的报告通常包含：

1. **执行摘要**：核心要点概述
2. **详细分析**：深入的技术解释
3. **实践指南**：具体的操作步骤
4. **最佳实践**：行业推荐做法
5. **参考资料**：信息来源链接

## 🔍 搜索功能详解

### 网络搜索 vs 知识库搜索

| 特性 | 网络搜索 | 知识库搜索 |
|------|----------|------------|
| **信息来源** | 互联网最新内容 | 内部专业文档 |
| **更新频率** | 实时 | 定期更新 |
| **内容质量** | 参差不齐 | 高质量专业内容 |
| **搜索速度** | 较慢 | 较快 |
| **适用场景** | 学习新技术、行业趋势 | 内部流程、专业知识 |

### 搜索状态指示器

界面会显示当前搜索状态：
- 🟢 **Web search available**：可以使用网络搜索
- 🟡 **Web search unavailable**：仅可使用知识库搜索
- 🔄 **Checking...**：正在检查搜索可用性

## 📊 进度监控

### 理解研究进度

研究过程中，您会看到以下类型的进度信息：

1. **🔍 搜索阶段**
   - "Searching: [查询内容]"
   - 显示AI正在搜索的具体问题

2. **📋 结果处理**
   - "Search Result: [相关性] [来源]"
   - 绿色标签表示相关，灰色表示不相关

3. **🤖 AI分析**
   - "Analyzing results..."
   - AI正在分析和整理信息

4. **📝 报告生成**
   - "Generating report..."
   - 正在创建最终报告

### 预估时间

- **快速研究**（深度1）：1-2分钟
- **标准研究**（深度2）：2-4分钟
- **深入研究**（深度3）：3-6分钟

## 🎨 界面功能详解

### 左侧控制面板

1. **研究主题框**
   - 支持多行输入
   - 可以输入详细的研究需求

2. **参数选择器**
   - 深度：控制研究的详细程度
   - 广度：控制涉及主题的范围

3. **网络搜索开关**
   - 动态检测可用性
   - 状态实时显示

4. **进度显示区**
   - 实时更新研究状态
   - 显示搜索结果和相关性

### 右侧报告区域

1. **报告显示**
   - Markdown格式渲染
   - 支持代码高亮
   - 支持图表和链接

2. **操作按钮**
   - Edit：进入编辑模式
   - Copy：复制到剪贴板
   - Download：下载文件

3. **反馈区域**
   - 文本输入框：描述调整需求
   - 文档上传：添加参考资料
   - Apply Feedback：应用反馈

---

**开始您的智能研究之旅吧！** 🚀

## 📖 附录：示例研究主题

### 技术类主题
- "Docker容器化技术的完整指南"
- "Kubernetes集群监控和日志管理最佳实践"
- "微服务架构中的服务发现和负载均衡"
- "CI/CD流水线设计和实现策略"

### 业务类主题
- "云原生应用开发的关键考虑因素"
- "企业级数据备份和灾难恢复策略"
- "DevOps文化在组织中的实施路径"
- "容器安全的威胁模型和防护措施"

### 学习类主题
- "从零开始学习Kubernetes的学习路径"
- "Python异步编程的概念和实践"
- "现代前端框架的选择和比较"
- "数据库性能优化的方法和工具"
