# Deep Research UI 视频教程脚本

## 🎬 教程概述
**时长**：5-8分钟  
**目标受众**：完全没有接触过Deep Research的新用户  
**教学目标**：让用户能够独立完成第一次研究

---

## 📝 脚本内容

### 开场介绍 (30秒)
**画面**：显示Deep Research主界面

**旁白**：
"欢迎使用Deep Research！这是一个智能研究助手，可以帮助您快速学习和了解任何主题。无论您是想学习新技术、了解行业趋势，还是查找专业知识，Deep Research都能为您生成专业的研究报告。

今天我们将通过一个简单的例子，教您如何在5分钟内完成第一次智能研究。"

### 界面介绍 (1分钟)
**画面**：鼠标指向界面各个部分

**旁白**：
"首先，让我们了解一下界面布局。

左侧是控制面板，这里有：
- 研究主题输入框：输入您想了解的内容
- 参数设置：控制研究的深度和广度
- 网络搜索开关：选择是否搜索互联网
- 进度显示：实时查看研究状态

右侧是报告区域，研究完成后会在这里显示详细的报告，您可以复制、下载或编辑报告内容。"

### 实际操作演示 (3分钟)
**画面**：实际操作演示

**旁白**：
"现在让我们进行一次实际的研究。假设我想了解'什么是Docker容器技术？'

第一步：在研究主题框中输入问题
[输入：'什么是Docker容器技术？它有什么优势？']

第二步：设置研究参数
对于新手，我建议：
- 深度选择'Standard'，这样可以获得详细但不过于复杂的信息
- 广度选择'Balanced'，平衡覆盖相关主题
- 保持网络搜索开启，这样可以获得最新的信息

第三步：点击'Start Research'按钮
[点击按钮]

现在系统开始工作了。您可以在左侧看到实时进度：
- 看，系统正在搜索相关信息
- 绿色标签表示找到了相关内容
- 灰色标签表示内容不太相关，会被过滤掉

整个过程大约需要2-4分钟，请耐心等待。"

### 结果查看 (1.5分钟)
**画面**：显示生成的报告

**旁白**：
"太好了！研究完成了。现在右侧显示了完整的研究报告。

报告包含：
- 执行摘要：Docker的核心概念
- 详细解释：容器技术的工作原理
- 优势分析：与传统虚拟化的比较
- 实际应用：具体的使用场景
- 参考资料：信息来源链接

如果您想保存这个报告，可以：
- 点击'Copy'复制到剪贴板
- 点击'Download'下载为文件

如果觉得报告需要改进，可以在下方的反馈框输入建议，比如'请添加更多实际例子'，然后点击'Apply Feedback'。"

### 高级功能介绍 (1分钟)
**画面**：演示编辑和反馈功能

**旁白**：
"Deep Research还有一些高级功能：

编辑功能：点击'Edit'按钮可以直接修改报告内容，满足您的特定需求。

反馈改进：如果您觉得报告缺少某些内容，可以在反馈框中描述，系统会自动改进报告。

文档上传：您还可以上传相关文档来增强研究内容。

网络搜索控制：如果您只想搜索内部知识库，可以关闭网络搜索开关。"

### 使用技巧分享 (1分钟)
**画面**：显示不同类型的搜索示例

**旁白**：
"最后分享几个使用技巧：

1. 写出好的研究主题：
   - 具体明确：'Docker容器化的优势和应用场景'
   - 避免太宽泛：不要只写'Docker'

2. 选择合适的参数：
   - 快速了解：深度1-2，广度2-3
   - 深入学习：深度2-3，广度3-4

3. 充分利用反馈功能：
   - '请用更简单的语言解释'
   - '请添加具体的代码示例'
   - '请比较不同的解决方案'

记住，Deep Research是您的智能研究伙伴，不要害怕尝试不同的主题和设置！"

### 结尾总结 (30秒)
**画面**：回到主界面

**旁白**：
"现在您已经学会了Deep Research的基本使用方法！

总结一下：
1. 输入具体的研究主题
2. 选择合适的参数设置
3. 耐心等待研究完成
4. 查看、编辑和保存报告
5. 使用反馈功能改进结果

开始您的智能研究之旅吧！如果需要更详细的帮助，请查看用户指南文档。"

---

## 🎯 拍摄要点

### 技术要求
- **分辨率**：1920x1080 或更高
- **帧率**：30fps
- **录制软件**：OBS Studio 或 Camtasia
- **音频**：清晰的旁白，无背景噪音

### 视觉要求
- **鼠标指针**：使用高亮鼠标指针
- **界面缩放**：适当放大重要区域
- **文字标注**：关键步骤添加文字说明
- **过渡效果**：平滑的场景切换

### 内容要求
- **语速**：适中，便于理解
- **停顿**：重要操作后适当停顿
- **重复**：关键步骤可以重复演示
- **互动**：鼓励观众跟着操作

---

## 📋 后期制作清单

### 必需元素
- [ ] 开场标题动画
- [ ] 界面区域高亮标注
- [ ] 关键步骤文字说明
- [ ] 进度条显示
- [ ] 结尾总结字幕

### 可选元素
- [ ] 背景音乐（轻柔）
- [ ] 转场动画效果
- [ ] 小贴士弹窗
- [ ] 相关链接注释

### 输出格式
- **主要格式**：MP4 (H.264)
- **备用格式**：WebM
- **字幕**：SRT格式
- **缩略图**：PNG格式，1280x720

---

## 🌐 发布渠道

### 内部发布
- 公司内部培训平台
- 知识库系统
- 团队协作工具

### 外部发布（可选）
- YouTube
- 企业官网
- 技术博客平台

### 配套材料
- 视频描述文本
- 相关文档链接
- 常见问题解答
- 用户反馈收集表单
