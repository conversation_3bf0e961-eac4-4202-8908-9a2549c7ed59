# Deep Research UI Video Tutorial Script

## 🎬 Tutorial Overview
**Duration**: 5-8 minutes  
**Target Audience**: Complete beginners who have never used Deep Research  
**Learning Objective**: Enable users to independently complete their first research

---

## 📝 Script Content

### Opening Introduction (30 seconds)
**Visual**: Display Deep Research main interface

**Narration**:
"Welcome to Deep Research! This is an intelligent research assistant that can help you quickly learn and understand any topic. Whether you want to learn new technologies, understand industry trends, or find professional knowledge, Deep Research can generate professional research reports for you.

Today we'll walk through a simple example to teach you how to complete your first intelligent research in just 5 minutes."

### Interface Introduction (1 minute)
**Visual**: Mouse pointing to various interface sections

**Narration**:
"First, let's understand the interface layout.

On the left is the control panel, which includes:
- Research topic input box: Enter what you want to learn about
- Parameter settings: Control research depth and breadth
- Web search toggle: Choose whether to search the internet
- Progress display: View research status in real-time

On the right is the report area, where detailed reports will be displayed after research completion. You can copy, download, or edit the report content here."

### Practical Operation Demo (3 minutes)
**Visual**: Actual operation demonstration

**Narration**:
"Now let's conduct an actual research. Suppose I want to understand 'What is Docker container technology?'

Step 1: Enter the question in the research topic box
[Type: 'What is Docker container technology? What are its advantages?']

Step 2: Set research parameters
For beginners, I recommend:
- Choose 'Standard' for depth, which provides detailed but not overly complex information
- Choose 'Balanced' for breadth, balancing coverage of related topics
- Keep web search enabled to get the latest information

Step 3: Click the 'Start Research' button
[Click button]

Now the system is working. You can see real-time progress on the left:
- Look, the system is searching for relevant information
- Green labels indicate relevant content was found
- Gray labels indicate content that's not very relevant and will be filtered out

The entire process takes about 2-4 minutes, please be patient."

### Result Viewing (1.5 minutes)
**Visual**: Display generated report

**Narration**:
"Excellent! The research is complete. Now the right side shows a complete research report.

The report includes:
- Executive summary: Core concepts of Docker
- Detailed explanation: How container technology works
- Advantage analysis: Comparison with traditional virtualization
- Practical applications: Specific use cases
- References: Information source links

If you want to save this report, you can:
- Click 'Copy' to copy to clipboard
- Click 'Download' to download as a file

If you think the report needs improvement, you can enter suggestions in the feedback box below, such as 'please add more practical examples', then click 'Apply Feedback'."

### Advanced Features Introduction (1 minute)
**Visual**: Demonstrate editing and feedback features

**Narration**:
"Deep Research also has some advanced features:

Edit function: Click the 'Edit' button to directly modify report content to meet your specific needs.

Feedback improvement: If you feel the report lacks certain content, you can describe it in the feedback box, and the system will automatically improve the report.

Document upload: You can also upload relevant documents to enhance research content.

Web search control: If you only want to search the internal knowledge base, you can turn off the web search toggle."

### Usage Tips Sharing (1 minute)
**Visual**: Show different types of search examples

**Narration**:
"Finally, let me share some usage tips:

1. Write good research topics:
   - Be specific and clear: 'Advantages and use cases of Docker containerization'
   - Avoid being too broad: Don't just write 'Docker'

2. Choose appropriate parameters:
   - Quick understanding: Depth 1-2, Breadth 2-3
   - In-depth learning: Depth 2-3, Breadth 3-4

3. Make full use of the feedback feature:
   - 'Please explain in simpler language'
   - 'Please add specific code examples'
   - 'Please compare different solutions'

Remember, Deep Research is your intelligent research partner, don't be afraid to try different topics and settings!"

### Closing Summary (30 seconds)
**Visual**: Return to main interface

**Narration**:
"Now you've learned the basic usage of Deep Research!

To summarize:
1. Enter specific research topics
2. Choose appropriate parameter settings
3. Wait patiently for research completion
4. View, edit, and save reports
5. Use feedback feature to improve results

Start your intelligent research journey! If you need more detailed help, please check the user guide documentation."

---

## 🎯 Filming Guidelines

### Technical Requirements
- **Resolution**: 1920x1080 or higher
- **Frame Rate**: 30fps
- **Recording Software**: OBS Studio or Camtasia
- **Audio**: Clear narration, no background noise

### Visual Requirements
- **Mouse Pointer**: Use highlighted mouse pointer
- **Interface Scaling**: Appropriately zoom important areas
- **Text Annotations**: Add text explanations for key steps
- **Transitions**: Smooth scene transitions

### Content Requirements
- **Speaking Speed**: Moderate, easy to understand
- **Pauses**: Appropriate pauses after important operations
- **Repetition**: Key steps can be demonstrated repeatedly
- **Interaction**: Encourage viewers to follow along

---

## 📋 Post-Production Checklist

### Required Elements
- [ ] Opening title animation
- [ ] Interface area highlighting annotations
- [ ] Key step text explanations
- [ ] Progress bar display
- [ ] Closing summary subtitles

### Optional Elements
- [ ] Background music (soft)
- [ ] Transition animation effects
- [ ] Tip pop-ups
- [ ] Related link annotations

### Output Formats
- **Primary Format**: MP4 (H.264)
- **Alternative Format**: WebM
- **Subtitles**: SRT format
- **Thumbnail**: PNG format, 1280x720

---

## 🌐 Distribution Channels

### Internal Distribution
- Company internal training platform
- Knowledge base system
- Team collaboration tools

### External Distribution (Optional)
- YouTube
- Corporate website
- Technical blog platforms

### Supporting Materials
- Video description text
- Related documentation links
- Frequently asked questions
- User feedback collection form
