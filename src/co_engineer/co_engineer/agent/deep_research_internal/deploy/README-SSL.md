# SSL Configuration for Deep Research System

This directory contains the SSL configuration for running the Deep Research system with HTTPS support using nginx as a reverse proxy.

## Overview

The setup includes:
- **nginx**: Reverse proxy server handling SSL termination
- **deep-research-ui**: Available at root path `/`
- **knowledgebase**: Available at path `/kb`
- **SSL**: Self-signed certificate for HTTPS

## Quick Start

1. **Generate SSL Certificate**:
   ```bash
   # For localhost only
   ./generate-ssl-cert.sh

   # With custom IP address
   ./generate-ssl-cert.sh *************
   ```

2. **Start Services**:
   ```bash
   docker compose up -d
   ```

3. **Access Services**:
   - Deep Research UI: https://localhost/
   - Knowledge Base: https://localhost/kb/

## File Structure

```
deploy/
├── docker-compose.yml      # Docker compose configuration with nginx
├── nginx.conf             # Nginx configuration file
├── generate-ssl-cert.sh   # Script to generate self-signed SSL certificate
├── ssl/                   # SSL certificates directory (created by script)
│   ├── server.crt        # SSL certificate
│   └── server.key        # SSL private key
└── README-SSL.md         # This file
```

## SSL Certificate Generation

The `generate-ssl-cert.sh` script creates a self-signed SSL certificate with the following features:

- **Validity**: 365 days
- **Key Size**: 2048 bits
- **Algorithm**: RSA with SHA-256
- **Usage**: `./generate-ssl-cert.sh [IP_ADDRESS]`
- **Subject Alternative Names**:
  - localhost
  - *.localhost
  - 127.0.0.1
  - ::1
  - Custom IP (if provided as parameter)

### Usage Examples

```bash
# Generate certificate for localhost only
./generate-ssl-cert.sh

# Generate certificate with additional IP address
./generate-ssl-cert.sh *************

# Generate certificate for server IP
./generate-ssl-cert.sh *********
```

The script will validate the IP address format and include it in the certificate's Subject Alternative Names, allowing secure access from that IP address.

### Certificate Details

- **Certificate**: `ssl/server.crt`
- **Private Key**: `ssl/server.key`
- **Common Name**: localhost
- **Organization**: Deep Research

## Nginx Configuration

The nginx configuration provides:

### SSL Settings
- TLS 1.2 and 1.3 support
- Strong cipher suites
- HSTS headers
- Security headers (X-Frame-Options, X-Content-Type-Options, etc.)

### Routing
- **Root path (`/`)**: Proxies to deep-research-ui:3000
- **Knowledge Base (`/kb`)**: Proxies to knowledgebase:9621
- **HTTP to HTTPS redirect**: All HTTP traffic redirected to HTTPS

### Features
- WebSocket support for real-time features
- Gzip compression
- Health check endpoint at `/health`
- Long timeout settings for AI processing

## Port Configuration

- **Port 443**: HTTPS traffic (main access point)
- **Port 80**: HTTP traffic (redirects to HTTPS)
- Internal services are not exposed externally

## Security Considerations

### Self-Signed Certificate Warning
Browsers will show a security warning for self-signed certificates. This is normal and expected. You can:

1. **Accept the warning** for development/testing
2. **Add certificate to trusted store** for repeated use
3. **Use a proper CA certificate** for production

### Production Recommendations
For production deployment:
- Use certificates from a trusted Certificate Authority (Let's Encrypt, etc.)
- Configure proper domain names
- Enable additional security headers
- Set up proper logging and monitoring

## Troubleshooting

### Certificate Issues
```bash
# Check certificate validity
openssl x509 -in ssl/server.crt -text -noout

# Verify certificate and key match
openssl x509 -noout -modulus -in ssl/server.crt | openssl md5
openssl rsa -noout -modulus -in ssl/server.key | openssl md5
```

### Service Issues
```bash
# Check service logs
docker compose logs nginx
docker compose logs deep-research-ui
docker compose logs knowledgebase

# Check service status
docker compose ps
```

### Network Issues
```bash
# Test SSL connection
openssl s_client -connect localhost:443 -servername localhost

# Test HTTP redirect
curl -I http://localhost/
```

## Customization

### Changing Certificate Details
Edit the variables in `generate-ssl-cert.sh`:
```bash
COUNTRY="US"
STATE="California"
CITY="San Francisco"
ORGANIZATION="Your Organization"
COMMON_NAME="your-domain.com"
```

### Modifying nginx Configuration
Edit `nginx.conf` to:
- Add custom headers
- Modify proxy settings
- Add rate limiting
- Configure additional locations

### Environment Variables
The docker-compose.yml supports all the same environment variables as the original configuration. Set them in your `.env` file or environment.

### Docker Compose Version
This configuration uses the modern `docker compose` command (Docker Compose V2). If you're using an older version, you may need to use `docker-compose` instead.

## Support

For issues related to:
- **SSL/nginx**: Check nginx logs and certificate validity
- **Deep Research UI**: Check application logs and environment variables
- **Knowledge Base**: Verify RAG service configuration and data volumes
