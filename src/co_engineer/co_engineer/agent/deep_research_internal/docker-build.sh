#!/bin/bash

# Deep Research UI Docker Build Script
# This script helps build and manage the Docker container for the Deep Research UI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="deep-research-ui"
TAG="latest"
CONTAINER_NAME="deep-research-ui"
PORT="3000"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build     Build the Docker image"
    echo "  run       Run the container"
    echo "  stop      Stop the running container"
    echo "  restart   Restart the container"
    echo "  logs      Show container logs"
    echo "  shell     Open a shell in the running container"
    echo "  clean     Remove container and image"
    echo "  compose   Use docker-compose (up/down/logs)"
    echo ""
    echo "Options:"
    echo "  -t, --tag TAG        Set image tag (default: latest)"
    echo "  -p, --port PORT      Set host port (default: 3000)"
    echo "  -n, --name NAME      Set container name (default: deep-research-ui)"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 run -p 8080"
    echo "  $0 compose up"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to build the Docker image
build_image() {
    print_status "Building Docker image: $IMAGE_NAME:$TAG"
    
    # Check if .env file exists, if not copy from example
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            print_warning ".env file not found. Copying from .env.example"
            cp .env.example .env
            print_warning "Please edit .env file with your actual API keys before running the container"
        else
            print_warning "No .env or .env.example file found. You may need to set environment variables manually."
        fi
    fi
    
    docker build --no-cache -t "$IMAGE_NAME:$TAG" .
    print_success "Image built successfully: $IMAGE_NAME:$TAG"
}

# Function to run the container
run_container() {
    print_status "Running container: $CONTAINER_NAME on port $PORT"
    
    # Stop existing container if running
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        print_warning "Stopping existing container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" > /dev/null
        docker rm "$CONTAINER_NAME" > /dev/null
    fi
    
    # Check if .env file exists
    ENV_FILE_ARG=""
    if [ -f .env ]; then
        ENV_FILE_ARG="--env-file .env"
    else
        print_warning "No .env file found. Using default environment variables."
    fi
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$PORT:3000" \
        $ENV_FILE_ARG \
        "$IMAGE_NAME:$TAG"
    
    print_success "Container started successfully"
    print_status "Access the application at: http://localhost:$PORT"
}

# Function to stop the container
stop_container() {
    print_status "Stopping container: $CONTAINER_NAME"
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        print_success "Container stopped and removed"
    else
        print_warning "Container $CONTAINER_NAME is not running"
    fi
}

# Function to restart the container
restart_container() {
    stop_container
    run_container
}

# Function to show logs
show_logs() {
    print_status "Showing logs for container: $CONTAINER_NAME"
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        docker logs -f "$CONTAINER_NAME"
    else
        print_error "Container $CONTAINER_NAME is not running"
        exit 1
    fi
}

# Function to open shell in container
open_shell() {
    print_status "Opening shell in container: $CONTAINER_NAME"
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        docker exec -it "$CONTAINER_NAME" /bin/sh
    else
        print_error "Container $CONTAINER_NAME is not running"
        exit 1
    fi
}

# Function to clean up
clean_up() {
    print_status "Cleaning up container and image"
    
    # Stop and remove container
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        print_success "Container removed"
    fi
    
    # Remove image
    if docker images -q "$IMAGE_NAME:$TAG" | grep -q .; then
        docker rmi "$IMAGE_NAME:$TAG"
        print_success "Image removed"
    fi
}

# Function to handle docker-compose commands
handle_compose() {
    # Try to use docker compose (newer) first, fallback to docker-compose (legacy)
    COMPOSE_CMD="docker compose"
    if ! command -v docker &> /dev/null || ! docker compose version &> /dev/null 2>&1; then
        if command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
        else
            print_error "Neither 'docker compose' nor 'docker-compose' is available"
            exit 1
        fi
    fi

    case "$1" in
        up)
            print_status "Starting services with $COMPOSE_CMD"
            $COMPOSE_CMD up -d
            print_success "Services started"
            ;;
        down)
            print_status "Stopping services with $COMPOSE_CMD"
            $COMPOSE_CMD down
            print_success "Services stopped"
            ;;
        logs)
            print_status "Showing $COMPOSE_CMD logs"
            $COMPOSE_CMD logs -f
            ;;
        *)
            print_error "Unknown compose command: $1"
            echo "Available compose commands: up, down, logs"
            exit 1
            ;;
    esac
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        build|run|stop|restart|logs|shell|clean|compose)
            COMMAND="$1"
            shift
            break
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
check_docker

# Execute the command
case "$COMMAND" in
    build)
        build_image
        ;;
    run)
        run_container
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    logs)
        show_logs
        ;;
    shell)
        open_shell
        ;;
    clean)
        clean_up
        ;;
    compose)
        if [[ $# -eq 0 ]]; then
            print_error "Compose command requires a subcommand (up/down/logs)"
            exit 1
        fi
        handle_compose "$1"
        ;;
    "")
        print_error "No command specified"
        show_usage
        exit 1
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
