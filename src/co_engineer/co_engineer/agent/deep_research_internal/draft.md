```mermaid
flowchart TD
    A[👤 User Input Research Topic] --> B{🔧 Configure Parameters}
    B --> C[📊 Depth Setting<br/>1-Quick, 2-Standard, 3-Deep]
    B --> D[📈 Breadth Setting<br/>2-Focused, 3-Balanced, 4-Broad]
    B --> E[🌐 Web Search Toggle<br/>Enable/Disable]
    
    C --> F[🚀 Start Research]
    D --> F
    E --> F
    
    F --> G{🔍 Search Phase}
    G -->|Web Search Enabled| H[🌐 Search Internet]
    G --> I[📚 Search Knowledge Base]
    
    H --> J[📋 Evaluate Search Results]
    I --> J
    
    J --> K[✅ Relevant Results]
    J --> L[❌ Irrelevant Results]
    
    K --> M[🤖 AI Analysis & Organization]
    L --> N[🗑️ Filter & Discard]
    
    M --> O[📝 Generate Research Report]
    O --> P[📄 Display Final Report]
    
    P --> Q{👤 User Actions}
    Q -->|Satisfied| R[📋 Copy/Download Report]
    Q -->|Need Improvement| S[💬 Provide Feedback]
    Q -->|Need Editing| T[✏️ Edit Report]
    
    S --> U[🔄 Apply Feedback & Improve]
    U --> O
    
    T --> V[📝 Manual Content Editing]
    V --> W[✅ Save Changes]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#fff3e0
    style R fill:#e0f2f1
```


```mermaid
graph TD

    subgraph "Retrieval Pipeline"
        User -- "Prompt" --> G1["NeMo Guardrails<br>(Optional)"]
        User -- "Prompt" --> QP["Query Processing"]
        G1 --> QP
        QP --> NRE1["NeMo Retriever<br>Embedding"]
        NRE1 --> VDB[("Vector Database<br>(cuVS)")]
        VDB --> NRR["NeMo Retriever<br>Reranking"]
        NRR --> LLM1["LLM"]
        LLM1 --> R{"Reflection"}
        R --> LLM2["LLM<br>(Optional)"]
        R --> G2["NeMo Guardrails<br>(Optional)"]
        LLM2 --> G2
        G2 -- "Response" --> User
    end

    subgraph "Extraction Pipeline"
        MED["Multimodal<br>Enterprise Data"] --> NRPE["NeMo Retriever<br>Page Elements"]
        NRPE -- "Pages as Images" --> NRP["NeMo Retriever<br>Parse"]
        NRPE -- "Tables" --> NRTS["NeMo Retriever<br>Table Structure"]
        NRPE -- "Infographics/Charts" --> NRGE["NeMo Retriever<br>Graphic Elements"]
        
        NRGE --> PO["PaddleOCR"]
        NRTS --> PO
        
        NRP -- "Text and Metadata" --> NRE2["NeMo Retriever<br>Embedding"]
        NRTS -- "Text and Metadata" --> NRE2
        PO -- "Text" --> NRE2
    end

    %% Connection between pipelines
    NRE2 --> VDB
```