```mermaid
graph TD

    subgraph "Retrieval Pipeline"
        User -- "Prompt" --> G1["NeMo Guardrails<br>(Optional)"]
        User -- "Prompt" --> QP["Query Processing"]
        G1 --> QP
        QP --> NRE1["NeMo Retriever<br>Embedding"]
        NRE1 --> VDB[("Vector Database<br>(cuVS)")]
        VDB --> NRR["NeMo Retriever<br>Reranking"]
        NRR --> LLM1["LLM"]
        LLM1 --> R{"Reflection"}
        R --> LLM2["LLM<br>(Optional)"]
        R --> G2["NeMo Guardrails<br>(Optional)"]
        LLM2 --> G2
        G2 -- "Response" --> User
    end

    subgraph "Extraction Pipeline"
        MED["Multimodal<br>Enterprise Data"] --> NRPE["NeMo Retriever<br>Page Elements"]
        NRPE -- "Pages as Images" --> NRP["NeMo Retriever<br>Parse"]
        NRPE -- "Tables" --> NRTS["NeMo Retriever<br>Table Structure"]
        NRPE -- "Infographics/Charts" --> NRGE["NeMo Retriever<br>Graphic Elements"]
        
        NRGE --> PO["PaddleOCR"]
        NRTS --> PO
        
        NRP -- "Text and Metadata" --> NRE2["NeMo Retriever<br>Embedding"]
        NRTS -- "Text and Metadata" --> NRE2
        PO -- "Text" --> NRE2
    end

    %% Connection between pipelines
    NRE2 --> VDB
```