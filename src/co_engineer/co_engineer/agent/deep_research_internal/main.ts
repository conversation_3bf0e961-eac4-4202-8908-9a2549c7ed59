import { google } from '@ai-sdk/google'
import { createOpenAI } from '@ai-sdk/openai';
import { generateObject, generateText, tool, wrapLanguageModel, defaultSettingsMiddleware} from 'ai'
import 'dotenv/config';
import { z } from 'zod'
import 'dotenv/config'
import Exa from 'exa-js'
import fs from 'fs'

// Set this to true to use OpenAI models, false to use Google models
const USE_OPENAI = process.env.USE_OPENAI === 'true';

// Initialize OpenAI client
const openai = createOpenAI({
    baseURL: process.env.OPENAI_API_BASE_URL,
    apiKey: process.env.OPENAI_API_KEY,
    compatibility: 'compatible',
});

// Default OpenAI models
const OPENAI_MODEL_TOOL = process.env.OPENAI_MODEL_TOOL || 'gpt-4o';
const OPENAI_MODEL_SUMMARY = process.env.OPENAI_MODEL_SUMMARY || 'gpt-4o';

// Select model based on USE_OPENAI flag
const mainModel = USE_OPENAI
    ? openai(OPENAI_MODEL_TOOL)
    : google('gemini-2.0-flash-001');

const summaryModel = USE_OPENAI
    ? openai(OPENAI_MODEL_SUMMARY)
    : google('gemini-2.0-flash-001');

// Define OpenAI model parameters to use in API calls
const openaiModelParams = {
    temperature: 0.7,
    top_p: 0.8,
    top_k: 20,
    max_tokens: 131072,
    presence_penalty: 1.5,
    chat_template_kwargs: {
      enable_thinking: true,
    },
};

// Initialize Exa search client only if API key is available
const EXA_API_KEY = process.env.EXA_API_KEY
const exa = EXA_API_KEY ? new Exa(EXA_API_KEY) : null

// RAG API configuration
const RAG_API_BASE_URL = process.env.RAG_API_BASE_URL || 'http://localhost:8000'
const RAG_API_KEY = process.env.RAG_API_KEY


type SearchResult = {
    title: string
    url: string
    content: string
    isKnowledgeBase?: boolean // Optional flag to identify knowledge base results
  }

type Learning = {
  learning: string
  followUpQuestions: string[]
}

type Research = {
  query: string | undefined
  queries: string[]
  searchResults: SearchResult[]
  learnings: Learning[]
  completedQueries: string[]
}

const SYSTEM_PROMPT = `You are an expert researcher. Today is ${new Date().toISOString()}. Follow these instructions when responding:
  - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.
  - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.
  - Be highly organized.
  - Suggest solutions that I didn't think about.
  - Be proactive and anticipate my needs.
  - Treat me as an expert in all subject matter.
  - Mistakes erode my trust, so be accurate and thorough.
  - Provide detailed explanations, I'm comfortable with lots of detail.
  - Value good arguments over authorities, the source is irrelevant.
  - Consider new technologies and contrarian ideas, not just the conventional wisdom.
  - You may use high levels of speculation or prediction, just flag it for me.
  - Use Markdown formatting.`


// Function to create a fresh research object for each session
const createFreshResearch = (): Research => ({
    query: undefined,
    queries: [],
    searchResults: [],
    learnings: [],
    completedQueries: [],
  })

const searchWeb = async (query: string) => {
    try {
      if (!exa) {
        console.warn('Web search is disabled: EXA_API_KEY not configured');
        return [];
      }

      console.log(`Executing web search for: "${query}"`);
      const { results } = await exa.searchAndContents(query, {
        numResults: 1,
        livecrawl: 'always',
      })

      // Validate results
      if (!results || results.length === 0) {
        console.warn('No search results found for query:', query);
        return [];
      }

      // Map and validate each result
      const validResults = results
        .filter(r => r && r.url && r.title) // Filter out invalid results
        .map(r => ({
          title: r.title || 'Untitled',
          url: r.url,
          content: r.text || 'No content available',
        }) as SearchResult);

      console.log(`Found ${validResults.length} valid results for query: "${query}"`);
      return validResults;
    } catch (error) {
      console.error('Error searching the web:', error);
      return []; // Return empty array on error
    }
  }

const searchKnowledgebase = async (query: string) => {
    try {
      console.log(`Executing knowledgebase search for: "${query}"`);

      // Prepare the request body according to the OpenAPI QueryRequest schema
      const requestBody = {
        query: query,
        mode: 'hybrid', // Default mode as specified in the schema
        // Configure token limits for better context retrieval
        max_token_for_text_unit: 131072, // 128k tokens
        max_token_for_global_context: 131072, // 128k tokens
        max_token_for_local_context: 131072, // 128k tokens
        // Custom user prompt to preserve details and avoid over-summarization
        user_prompt: `Please provide a comprehensive and detailed response to the query. Do not summarize or condense the information - include all relevant details, technical specifications, examples, and context. The response will be further processed by another system, so preserving complete information is more important than brevity. Include specific steps, code examples, configuration details, and any nuances that might be relevant to the topic.`,
        // Optional parameters can be added here if needed
        // top_k: 5,
        // response_type: 'Multiple Paragraphs',
        // only_need_context: false,
        // only_need_prompt: false
      };

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add API key to query parameters if available
      const url = new URL('/query', RAG_API_BASE_URL);
      if (RAG_API_KEY) {
        url.searchParams.append('api_key_header_value', RAG_API_KEY);
      }

      // Make the API call
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`RAG API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Validate the response according to QueryResponse schema
      if (!data || typeof data.response !== 'string') {
        console.warn('Invalid response format from RAG API:', data);
        return [];
      }

      // Convert the RAG response to SearchResult format to match the expected interface
      const searchResult: SearchResult = {
        title: `Knowledge Base Result for: ${query}`,
        url: `${RAG_API_BASE_URL}`, // Use the API endpoint as the "URL"
        content: data.response,
        isKnowledgeBase: true, // Add flag to identify knowledge base results
      };

      console.log(`Found knowledgebase result for query: "${query}"`);
      return [searchResult];

    } catch (error) {
      console.error('Error searching the knowledgebase:', error);
      return []; // Return empty array on error
    }
  }

const generateSearchQueries = async (query: string, n: number = 3) => {
    const {
      object: { queries },
    } = await generateObject({
      providerOptions: {
        chat_template_kwargs: {
          enable_thinking: false,
        },
      },
      model: mainModel,
      prompt: `Generate ${n} search queries for the following query: ${query}`,
      schema: z.object({
        queries: z.array(z.string()).min(1).max(5),
      }),
      ...(USE_OPENAI ? openaiModelParams : {}),
    })
    return queries
  }

  const searchAndProcess = async (
    query: string,
    accumulatedSources: SearchResult[],
    enableWebSearch: boolean = true
  ) => {
    const finalSearchResults: SearchResult[] = []

    try {
      await generateText({
        providerOptions: {
          chat_template_kwargs: {
            enable_thinking: false,
          },
        },
        model: mainModel,
        prompt: `Search for information about ${query}`,
        system: (enableWebSearch && EXA_API_KEY)
          ? 'You are a researcher. For each query, you MUST use BOTH search methods: 1) First use searchKnowledgebase to find internal documentation, 2) Then use searchWeb to find current web information. Always use both tools for comprehensive research - do not skip either one. The search tools will automatically evaluate results for relevance and add useful information to the research database.'
          : 'You are a researcher. For each query, search for information using searchKnowledgebase for internal documentation. The search tool will automatically evaluate results for relevance and add useful information to the research database. Note: Web search is disabled for this research session.',
        maxSteps: 5,
        ...(USE_OPENAI ? openaiModelParams : {}),
        tools: (() => {
          const baseTools: any = {
            // Always include knowledgebase search
            searchKnowledgebase: tool({
              description: 'Search the internal knowledgebase for information about a given query',
              parameters: z.object({
                query: z.string().min(1),
              }),
              async execute({ query }) {
                console.log(`Tool searchKnowledgebase called with query: "${query}"`);
                const results = await searchKnowledgebase(query)

                if (results.length === 0) {
                  console.warn('No knowledgebase results found for query:', query);
                  return { results: [], message: 'No knowledgebase results found. Try a different query or search the web instead.' };
                }

                // Immediately evaluate and process the results
                for (const result of results) {
                  try {
                    const { object: evaluation } = await generateObject({
                      model: mainModel,
                      prompt: `Evaluate whether the search results are relevant and will help answer the following query: ${query}.

                      <search_results>
                      ${JSON.stringify(result)}
                      </search_results>
                      `,
                      output: 'enum',
                      enum: ['relevant', 'irrelevant'],
                      ...(USE_OPENAI ? openaiModelParams : {}),
                    })

                    if (evaluation === 'relevant') {
                      finalSearchResults.push(result)
                    }

                    console.log('Found:', result.url)
                    console.log('Evaluation completed:', evaluation)

                  } catch (error) {
                    console.error('Error evaluating search result:', error);
                  }
                }

                return {
                  results,
                  message: `Found ${results.length} knowledgebase results for "${query}". ${finalSearchResults.length} relevant results added.`
                }
              },
            }),
            checkStatus: tool({
              description: 'Check the current status of search results and research progress',
              parameters: z.object({}),
              async execute() {
                return `Research status: Found ${finalSearchResults.length} relevant results so far. Continue searching or proceed with analysis.`;
              },
            }),
          };

          // Conditionally add web search tool (only if EXA_API_KEY is available)
          if (enableWebSearch && EXA_API_KEY) {
            baseTools.searchWeb = tool({
              description: 'Search the web for information about a given query',
              parameters: z.object({
                query: z.string().min(1),
              }),
              async execute({ query }) {
                console.log(`Tool searchWeb called with query: "${query}"`);
                const results = await searchWeb(query)

                if (results.length === 0) {
                  console.warn('No results found for query:', query);
                  return { results: [], message: 'No results found. Try a different query.' };
                }

                // Immediately evaluate and process the results
                for (const result of results) {
                  try {
                    const { object: evaluation } = await generateObject({
                      model: mainModel,
                      prompt: `Evaluate whether the search results are relevant and will help answer the following query: ${query}. If the page already exists in the existing results, mark it as irrelevant.

                      <search_results>
                      ${JSON.stringify(result)}
                      </search_results>

                      <existing_results>
                      ${JSON.stringify(accumulatedSources.map((r) => r.url))}
                      </existing_results>

                      `,
                      output: 'enum',
                      enum: ['relevant', 'irrelevant'],
                      ...(USE_OPENAI ? openaiModelParams : {}),
                    })

                    if (evaluation === 'relevant') {
                      finalSearchResults.push(result)
                    }

                    console.log('Found:', result.url)
                    console.log('Evaluation completed:', evaluation)

                  } catch (error) {
                    console.error('Error evaluating search result:', error);
                  }
                }

                return {
                  results,
                  message: `Found ${results.length} results for "${query}". ${finalSearchResults.length} relevant results added.`
                }
              },
            });
          }

          return baseTools;
        })(),
      })

    } catch (error) {
      console.error('Error in searchAndProcess:', error);
    }
    return finalSearchResults
  }


  const generateLearnings = async (query: string, searchResult: SearchResult) => {
    const { object } = await generateObject({
      model: mainModel,
      prompt: `The user is researching "${query}". The following search result were deemed relevant.
      Generate a learning and a follow-up question from the following search result:

      <search_result>
      ${JSON.stringify(searchResult)}
      </search_result>
      `,
      schema: z.object({
        learning: z.string(),
        followUpQuestions: z.array(z.string()),
      }),
      ...(USE_OPENAI ? openaiModelParams : {}),
    })
    return object
  }

// Define event emitter interface
export interface EventEmitter {
  emit(event: string, data: any): void;
}

// Null event emitter for when no events need to be emitted
const nullEventEmitter: EventEmitter = {
  emit: () => {}
};

// Define the main research function with internal research state management
export const deepResearch = async (
  prompt: string,
  depth: number = 2,
  breadth: number = 2,
  eventEmitter: EventEmitter = nullEventEmitter,
  enableWebSearch: boolean = true,
  accumulatedResearch?: Research
) => {
  // Create a fresh research object if this is the top-level call
  if (!accumulatedResearch) {
    accumulatedResearch = createFreshResearch();
  }

  if (!accumulatedResearch.query) {
    accumulatedResearch.query = prompt
  }

  if (depth === 0) {
    return accumulatedResearch
  }

  const queries = await generateSearchQueries(prompt, breadth)
  accumulatedResearch.queries = queries

  for (const query of queries) {
    console.log(`Searching the web for: ${query}`)

    // Emit search started event
    eventEmitter.emit('searchStarted', { query });

    const searchResults = await searchAndProcess(
      query,
      accumulatedResearch.searchResults,
      enableWebSearch
    )

    accumulatedResearch.searchResults.push(...searchResults)

    for (const searchResult of searchResults) {
      console.log(`Processing search result: ${searchResult.url}`)

      // Emit search completed event
      eventEmitter.emit('searchCompleted', {
        query,
        url: searchResult.url,
        relevant: true,
        isKnowledgeBase: searchResult.isKnowledgeBase || false
      });

      const learnings = await generateLearnings(query, searchResult)
      accumulatedResearch.learnings.push(learnings)
      accumulatedResearch.completedQueries.push(query)

      // Emit learning extracted event
      eventEmitter.emit('learningExtracted', {
        learning: learnings.learning,
        followUpQuestions: learnings.followUpQuestions
      });

      const newQuery = `Overall research goal: ${prompt}
        Previous search queries: ${accumulatedResearch.completedQueries.join(', ')}

        Follow-up questions: ${learnings.followUpQuestions.join(', ')}
        `
      await deepResearch(newQuery, depth - 1, Math.ceil(breadth / 2), eventEmitter, enableWebSearch, accumulatedResearch)
    }
  }
  return accumulatedResearch
}

// Function to generate a step-by-step guide for how-to questions
export const generateStepByStepGuide = async (research: Research) => {
  // Use the same model selection logic based on USE_OPENAI flag
  const guideModel = summaryModel

  const GUIDE_SYSTEM_PROMPT = `You are an expert at creating clear, detailed step-by-step guides. Today is ${new Date().toISOString()}. Follow these instructions when responding:
    - Create a comprehensive, actionable guide that walks the user through each step of the process
    - Break down complex tasks into smaller, manageable steps
    - Number each step clearly and provide detailed instructions for each
    - Include prerequisites or requirements at the beginning
    - Add troubleshooting tips for common issues that might arise
    - Use clear, concise language that is easy to follow
    - Highlight important warnings or cautions in bold
    - Include code snippets, commands, or configuration examples where relevant
    - Conclude with verification steps to ensure success
    - Use Markdown formatting for better readability`

  const { text } = await generateText({
    model: guideModel,
    system: GUIDE_SYSTEM_PROMPT,
    prompt:
      'Generate a detailed step-by-step guide based on the following research data. The user wants to know how to perform a specific task, so focus on creating a clear, actionable guide with numbered steps:\n\n' +
      JSON.stringify(research, null, 2),
    ...(USE_OPENAI ? openaiModelParams : {}),
  })
  return text
}

// Export the generateReport function
export const generateReport = async (research: Research) => {
    // Use the same model selection logic based on USE_OPENAI flag
    const reportModel = summaryModel

    const { text } = await generateText({
      model: reportModel,
      system: SYSTEM_PROMPT,
      prompt:
        'Generate a report based on the following research data:\n\n' +
        JSON.stringify(research, null, 2),
      ...(USE_OPENAI ? openaiModelParams : {}),
    })
    return text
  }

// Function to determine if a query is a how-to question
export const isHowToQuestion = async (query: string): Promise<boolean> => {
  const { object } = await generateObject({
    model: mainModel,
    prompt: `Analyze the following query and determine if it's asking "how to do something" (a procedural question requiring step-by-step instructions) or if it's asking for general information/explanation:

Query: "${query}"

Examples of "how to" questions:
- How to install Ubuntu on a bare metal server
- How to configure a Kubernetes cluster
- How to set up a VPN server
- Steps to migrate a database
- Guide for implementing OAuth2

Examples of general information questions:
- What is machine learning?
- Explain quantum computing
- Compare REST and GraphQL
- History of the internet
- Benefits of microservices architecture`,
    schema: z.object({
      isHowToQuestion: z.boolean(),
      reasoning: z.string(),
    }),
    ...(USE_OPENAI ? openaiModelParams : {}),
  })

  return object.isHowToQuestion
}

// Agent selector function that chooses between step-by-step guide and general report
export const selectAgentAndGenerateReport = async (research: Research): Promise<string> => {
  // If the query is undefined, default to general report
  if (!research.query) {
    return generateReport(research)
  }

  // Determine if this is a how-to question
  const isHowTo = await isHowToQuestion(research.query)

  // Select the appropriate agent based on the query type
  if (isHowTo) {
    console.log('Query identified as a how-to question. Generating step-by-step guide...')
    return generateStepByStepGuide(research)
  } else {
    console.log('Query identified as a general information question. Generating research report...')
    return generateReport(research)
  }
}

// Export the searchKnowledgebase function for external use
export { searchKnowledgebase };

// deepResearch is already exported above

// Only run the main function if this file is executed directly
if (require.main === module) {
  const main = async () => {
    const research = await deepResearch(
      // 'How to create an ubuntu autoinstall iso.'
      'How to install unattended ubuntu to a bare metal server by using redfish'
    )
    console.log('Research completed!')
    console.log('Selecting appropriate agent and generating report...')

    // Use the agent selector to choose the appropriate report type
    const report = await selectAgentAndGenerateReport(research)

    console.log('Report generated! report.md')
    fs.writeFileSync('report.md', report)
  }

  main()
}
