#!/usr/bin/env tsx

/**
 * Test script to verify that AI actively uses both search methods when web search is enabled
 */

import { deepResearch } from './main';

// Enhanced mock event emitter for testing
const mockEventEmitter = {
  emit: (event: string, data: any) => {
    console.log(`📡 Event: ${event}`, data);
  }
};

// Track tool usage
let toolUsageStats = {
  searchKnowledgebase: 0,
  searchWeb: 0
};

// Mock the console.log to track tool calls
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  const message = args.join(' ');
  
  // Track tool usage
  if (message.includes('Tool searchKnowledgebase called')) {
    toolUsageStats.searchKnowledgebase++;
    originalConsoleLog('🔍 KNOWLEDGEBASE SEARCH:', message);
  } else if (message.includes('Tool searchWeb called')) {
    toolUsageStats.searchWeb++;
    originalConsoleLog('🌐 WEB SEARCH:', message);
  } else {
    originalConsoleLog(...args);
  }
};

async function testDualSearchUsage() {
  console.log('🧪 Testing Dual Search Usage (Web Search Enabled)\n');
  
  const testQuery = 'How to configure Ubuntu cloud-init networking';
  const depth = 1;
  const breadth = 2;
  
  // Reset stats
  toolUsageStats = { searchKnowledgebase: 0, searchWeb: 0 };
  
  console.log('=== Testing with Web Search ENABLED ===');
  console.log('Expected: AI should use BOTH searchKnowledgebase AND searchWeb for each query');
  console.log(`Test Query: "${testQuery}"`);
  console.log(`Depth: ${depth}, Breadth: ${breadth}\n`);
  
  try {
    const research = await deepResearch(
      testQuery,
      depth,
      breadth,
      mockEventEmitter,
      true // enableWebSearch = true
    );
    
    console.log('\n=== RESULTS ===');
    console.log('✅ Research completed successfully');
    console.log(`📊 Search Results Found: ${research.searchResults.length}`);
    console.log(`📝 Queries Generated: ${research.queries.length}`);
    console.log('\n=== TOOL USAGE STATISTICS ===');
    console.log(`🔍 searchKnowledgebase calls: ${toolUsageStats.searchKnowledgebase}`);
    console.log(`🌐 searchWeb calls: ${toolUsageStats.searchWeb}`);
    
    // Analysis
    console.log('\n=== ANALYSIS ===');
    if (toolUsageStats.searchKnowledgebase > 0 && toolUsageStats.searchWeb > 0) {
      console.log('✅ SUCCESS: AI used both search methods');
      
      if (toolUsageStats.searchKnowledgebase >= research.queries.length && 
          toolUsageStats.searchWeb >= research.queries.length) {
        console.log('🎯 EXCELLENT: AI used both methods for multiple queries');
      } else {
        console.log('⚠️  PARTIAL: AI used both methods but not consistently for all queries');
      }
    } else if (toolUsageStats.searchKnowledgebase > 0) {
      console.log('❌ ISSUE: AI only used knowledgebase search');
    } else if (toolUsageStats.searchWeb > 0) {
      console.log('❌ ISSUE: AI only used web search');
    } else {
      console.log('❌ ERROR: AI did not use any search tools');
    }
    
    // Recommendations
    console.log('\n=== RECOMMENDATIONS ===');
    const totalQueries = research.queries.length;
    const expectedKnowledgebaseCalls = totalQueries;
    const expectedWebCalls = totalQueries;
    
    if (toolUsageStats.searchKnowledgebase < expectedKnowledgebaseCalls) {
      console.log(`📋 Need more knowledgebase searches: ${toolUsageStats.searchKnowledgebase}/${expectedKnowledgebaseCalls}`);
    }
    
    if (toolUsageStats.searchWeb < expectedWebCalls) {
      console.log(`📋 Need more web searches: ${toolUsageStats.searchWeb}/${expectedWebCalls}`);
    }
    
    if (toolUsageStats.searchKnowledgebase >= expectedKnowledgebaseCalls && 
        toolUsageStats.searchWeb >= expectedWebCalls) {
      console.log('🎉 Perfect! AI is using both search methods as expected');
    }
    
  } catch (error) {
    console.error('❌ Error during research:', error);
  }
  
  console.log('\n🏁 Dual search usage test completed');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDualSearchUsage().catch(console.error);
}

export { testDualSearchUsage };
