#!/usr/bin/env tsx

/**
 * Test script to verify that knowledge base results are properly identified and displayed
 */

import { deepResearch } from './main';

// Enhanced mock event emitter for testing
const mockEventEmitter = {
  emit: (event: string, data: any) => {
    if (event === 'searchCompleted') {
      console.log(`📡 searchCompleted Event:`, {
        query: data.query,
        url: data.url,
        relevant: data.relevant,
        isKnowledgeBase: data.isKnowledgeBase
      });
      
      // Simulate UI display logic
      const badge = data.relevant ?
        '✅ Relevant' :
        '❌ Not relevant';

      let linkDisplay;
      if (data.isKnowledgeBase) {
        linkDisplay = `📚 Knowledge Base -> ${data.url}`;
        console.log(`🎯 UI Display: ${badge} ${linkDisplay}`);
      } else {
        linkDisplay = `🌐 Web -> ${data.url}`;
        console.log(`🎯 UI Display: ${badge} ${linkDisplay}`);
      }
    } else {
      console.log(`📡 Event: ${event}`, data);
    }
  }
};

async function testKnowledgeBaseDisplay() {
  console.log('🧪 Testing Knowledge Base Display Functionality\n');
  
  const testQuery = 'How to configure Ubuntu autoinstall';
  const depth = 1;
  const breadth = 2;
  
  console.log('=== Testing Knowledge Base vs Web Search Display ===');
  console.log('Expected: Knowledge base results should show "📚 Knowledge Base" instead of URL');
  console.log('Expected: Web search results should show truncated URL');
  console.log(`Test Query: "${testQuery}"`);
  console.log(`Depth: ${depth}, Breadth: ${breadth}\n`);
  
  try {
    const research = await deepResearch(
      testQuery,
      depth,
      breadth,
      mockEventEmitter,
      true // enableWebSearch = true
    );
    
    console.log('\n=== RESULTS SUMMARY ===');
    console.log('✅ Research completed successfully');
    console.log(`📊 Total Search Results: ${research.searchResults.length}`);
    
    // Analyze results
    let knowledgeBaseCount = 0;
    let webSearchCount = 0;
    
    for (const result of research.searchResults) {
      if (result.isKnowledgeBase) {
        knowledgeBaseCount++;
        console.log(`📚 Knowledge Base Result: ${result.title}`);
      } else {
        webSearchCount++;
        console.log(`🌐 Web Search Result: ${result.title}`);
      }
    }
    
    console.log('\n=== ANALYSIS ===');
    console.log(`📚 Knowledge Base Results: ${knowledgeBaseCount}`);
    console.log(`🌐 Web Search Results: ${webSearchCount}`);
    
    if (knowledgeBaseCount > 0 && webSearchCount > 0) {
      console.log('🎉 SUCCESS: Both knowledge base and web search results found');
      console.log('✅ UI should display different link styles for each type');
    } else if (knowledgeBaseCount > 0) {
      console.log('📚 Only knowledge base results found');
    } else if (webSearchCount > 0) {
      console.log('🌐 Only web search results found');
    } else {
      console.log('❌ No results found');
    }
    
  } catch (error) {
    console.error('❌ Error during research:', error);
  }
  
  console.log('\n🏁 Knowledge base display test completed');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testKnowledgeBaseDisplay().catch(console.error);
}

export { testKnowledgeBaseDisplay };
