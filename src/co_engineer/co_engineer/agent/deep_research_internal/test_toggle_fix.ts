#!/usr/bin/env tsx

/**
 * Test script to verify that the web search toggle functionality works correctly
 */

import { deepResearch } from './main';

// Mock event emitter for testing
const mockEventEmitter = {
  emit: (event: string, data: any) => {
    console.log(`📡 Event: ${event}`, data);
  }
};

async function testWebSearchToggle() {
  console.log('🧪 Testing Web Search Toggle Functionality\n');
  
  const testQuery = 'How to configure Ubuntu autoinstall';
  const depth = 1;
  const breadth = 2;
  
  console.log('=== Test 1: Web Search ENABLED ===');
  console.log('Expected: Should use both searchKnowledgebase and searchWeb tools');
  try {
    const researchWithWeb = await deepResearch(
      testQuery,
      depth,
      breadth,
      mockEventEmitter,
      true // enableWebSearch = true
    );
    console.log('✅ Research with web search completed');
    console.log(`📊 Results: ${researchWithWeb.searchResults.length} search results found\n`);
  } catch (error) {
    console.error('❌ Error in web search enabled test:', error);
  }
  
  console.log('=== Test 2: Web Search DISABLED ===');
  console.log('Expected: Should use only searchKnowledgebase tool');
  try {
    const researchWithoutWeb = await deepResearch(
      testQuery,
      depth,
      breadth,
      mockEventEmitter,
      false // enableWebSearch = false
    );
    console.log('✅ Research without web search completed');
    console.log(`📊 Results: ${researchWithoutWeb.searchResults.length} search results found\n`);
  } catch (error) {
    console.error('❌ Error in web search disabled test:', error);
  }
  
  console.log('🏁 Toggle functionality test completed');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testWebSearchToggle().catch(console.error);
}

export { testWebSearchToggle };
