#!/usr/bin/env tsx

/**
 * <PERSON><PERSON> test to verify UI display logic for knowledge base vs web search results
 */

// Mock event emitter that simulates both types of results
const mockEventEmitter = {
  emit: (event: string, data: any) => {
    console.log(`📡 Event: ${event}`, data);
  }
};

function testUIDisplayLogic() {
  console.log('🧪 Testing UI Display Logic (Mock)\n');
  
  // Simulate different types of search results
  const testResults = [
    {
      query: 'Test query 1',
      url: 'http://***************:9621',
      relevant: true,
      isKnowledgeBase: true
    },
    {
      query: 'Test query 2', 
      url: 'https://ubuntu.com/server/docs/install/autoinstall-quickstart',
      relevant: true,
      isKnowledgeBase: false
    },
    {
      query: 'Test query 3',
      url: 'http://localhost:8000',
      relevant: true,
      isKnowledgeBase: true
    },
    {
      query: 'Test query 4',
      url: 'https://cloudinit.readthedocs.io/en/stable/topics/network-config.html',
      relevant: false,
      isKnowledgeBase: false
    }
  ];
  
  console.log('=== Simulating UI Display Logic ===\n');
  
  testResults.forEach((data, index) => {
    console.log(`--- Test Result ${index + 1} ---`);
    console.log(`Query: ${data.query}`);
    console.log(`URL: ${data.url}`);
    console.log(`Relevant: ${data.relevant}`);
    console.log(`Is Knowledge Base: ${data.isKnowledgeBase}`);
    
    // Simulate the UI display logic from script.js
    const badge = data.relevant ?
      '✅ Relevant' :
      '❌ Not relevant';

    let linkText, linkHtml;
    if (data.isKnowledgeBase) {
      linkText = 'Knowledge Base';
      linkHtml = `📚 ${linkText} -> ${data.url}`;
    } else {
      // Simulate truncateUrl function
      linkText = truncateUrl(data.url);
      linkHtml = `🌐 ${linkText}`;
    }
    
    console.log(`🎯 UI Display: ${badge} ${linkHtml}`);
    console.log('');
  });
  
  console.log('=== Summary ===');
  const knowledgeBaseCount = testResults.filter(r => r.isKnowledgeBase).length;
  const webSearchCount = testResults.filter(r => !r.isKnowledgeBase).length;
  
  console.log(`📚 Knowledge Base Results: ${knowledgeBaseCount}`);
  console.log(`🌐 Web Search Results: ${webSearchCount}`);
  console.log('✅ UI display logic working correctly!');
}

// Helper function to simulate truncateUrl (from script.js)
function truncateUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname + (urlObj.pathname.length > 20 ?
      urlObj.pathname.substring(0, 20) + '...' : urlObj.pathname);
  } catch (e) {
    return url.length > 40 ? url.substring(0, 40) + '...' : url;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testUIDisplayLogic();
}

export { testUIDisplayLogic };
